# Инструкция по переходу на реальный API

## Введение

Этот документ содержит инструкции по переходу с локальных JSON-файлов на реальный API. В текущей реализации проект использует локальные JSON-файлы (`products.json` и `data.json`) для хранения данных, но подготовлен для легкого перехода на реальный API.

## Структура API

Проект ожидает следующие API endpoints:

1. **GET /api/products** - получение списка всех продуктов
2. **GET /api/products/:id** - получение детальной информации о продукте по ID
3. **GET /api/catalog** - получение структуры каталога
4. **GET /api/products?department=X&section=Y&subsection=Z&group=W** - фильтрация продуктов по категории
5. **GET /api/products?search=query** - поиск продуктов по запросу

## Формат данных

### Продукты

Ожидаемый формат данных для продуктов:

```json
{
  "id": 1,
  "title": "Название продукта",
  "image": "/path/to/image.jpg",
  "retailPrice": 1000,
  "wholesalePrice": 900,
  "suppliers": 5,
  "department": "21",
  "section": "21-02",
  "subsection": "21-0201",
  "group": "21-020101",
  "suppliersList": [
    {
      "id": 1,
      "name": "Название поставщика",
      "manager": "Имя менеджера",
      "retail": "1 000 ₸",
      "wholesale": "900 ₸",
      "unit": "шт",
      "region": "Регион"
    }
  ],
  "specs": {
    "key1": "value1",
    "key2": "value2"
  },
  "description": "Описание продукта"
}
```

### Каталог

Ожидаемый формат данных для каталога:

```json
{
  "21": {
    "name": "Название отдела",
    "sections": {
      "21-02": {
        "name": "Название раздела",
        "subsections": {
          "21-0201": {
            "name": "Название подраздела",
            "groups": {
              "21-020101": "Название группы"
            }
          }
        }
      }
    }
  }
}
```

## Настройка переменных окружения

Для перехода на реальный API необходимо изменить переменные окружения в файле `.env`:

```
REACT_APP_USE_LOCAL_JSON=false
REACT_APP_API_URL=https://your-api-url.com
```

## Файлы, отвечающие за работу с API

1. **src/config/api.js** - конфигурация API
2. **src/services/api.service.js** - сервисные функции для работы с API

## Компоненты, использующие API

1. **PriceList.jsx** - загрузка списка продуктов, фильтрация, поиск
2. **ProductDetail.jsx** - загрузка детальной информации о продукте
3. **Catalog.jsx** - загрузка структуры каталога

## Тестирование перехода на API

1. Убедитесь, что API возвращает данные в ожидаемом формате
2. Измените переменные окружения для использования реального API
3. Запустите приложение и проверьте работу всех функций:
   - Загрузка списка продуктов
   - Просмотр детальной информации о продукте
   - Фильтрация продуктов по категории
   - Поиск продуктов
   - Работа каталога

## Возможные проблемы и их решение

1. **Неверный формат данных** - убедитесь, что API возвращает данные в ожидаемом формате
2. **CORS-ошибки** - настройте CORS на сервере API
3. **Ошибки авторизации** - добавьте заголовки авторизации в `src/config/api.js`
4. **Различия в структуре данных** - адаптируйте `src/services/api.service.js` под структуру данных вашего API
