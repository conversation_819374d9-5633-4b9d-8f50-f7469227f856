// Простой тест API для проверки работы с фотографиями
import ApiService from './src/services/api.service.js';

async function testPhotosAPI() {
  try {
    console.log('Тестируем API фотографий...');
    
    // Получаем все фотографии
    const photos = await ApiService.getPhotos();
    console.log('Всего фотографий:', photos.length);
    
    if (photos.length > 0) {
      console.log('Пример фотографии:', photos[0]);
      
      // Тестируем получение фотографий для конкретного товара
      const materialId = photos[0].MaterialId;
      const productPhotos = await ApiService.getProductPhotos(materialId);
      console.log(`Фотографий для товара ${materialId}:`, productPhotos.length);
      
      // Тестируем получение главной фотографии
      const mainPhoto = await ApiService.getProductMainPhoto(materialId);
      console.log('Главная фотография:', mainPhoto);
    }
    
  } catch (error) {
    console.error('Ошибка при тестировании API:', error);
  }
}

// Запускаем тест только если файл запущен напрямую
if (typeof window === 'undefined') {
  testPhotosAPI();
}
