import React from "react";
import styled, { keyframes } from "styled-components";

// Анимация для спиннера
const spin = keyframes`
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
`;

// Анимация для пульсации
const pulse = keyframes`
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
`;

// Анимация для прогресс-бара
const progressAnimation = keyframes`
  0% { width: 0%; }
  100% { width: var(--progress-width); }
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  min-height: ${props => props.fullHeight ? '50vh' : '200px'};
`;

const Spinner = styled.div`
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #0066cc;
  border-radius: 50%;
  animation: ${spin} 1s linear infinite;
  margin-bottom: 16px;
`;

const LoadingText = styled.div`
  font-size: 16px;
  color: #666;
  margin-bottom: 8px;
  animation: ${pulse} 2s ease-in-out infinite;
`;

const SubText = styled.div`
  font-size: 14px;
  color: #999;
  margin-bottom: 16px;
`;

const ProgressContainer = styled.div`
  width: 100%;
  max-width: 300px;
  height: 6px;
  background-color: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 8px;
`;

const ProgressBar = styled.div`
  height: 100%;
  background: linear-gradient(90deg, #0066cc, #0088ff);
  border-radius: 3px;
  transition: width 0.3s ease;
  width: ${props => props.progress}%;
  animation: ${props => props.animated ? progressAnimation : 'none'} 0.3s ease;
`;

const ProgressText = styled.div`
  font-size: 12px;
  color: #888;
  margin-top: 4px;
`;

const StatsContainer = styled.div`
  margin-top: 16px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 8px;
  font-size: 12px;
  color: #666;
  max-width: 300px;
`;

const StatRow = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  
  &:last-child {
    margin-bottom: 0;
  }
`;

/**
 * Компонент индикатора загрузки с прогрессом
 */
const LoadingIndicator = ({
  text = "Загрузка...",
  subText = "",
  progress = 0,
  showProgress = false,
  showStats = false,
  stats = {},
  fullHeight = false,
  animated = true
}) => {
  return (
    <LoadingContainer fullHeight={fullHeight}>
      <Spinner />
      <LoadingText>{text}</LoadingText>
      {subText && <SubText>{subText}</SubText>}
      
      {showProgress && (
        <>
          <ProgressContainer>
            <ProgressBar progress={progress} animated={animated} />
          </ProgressContainer>
          <ProgressText>{Math.round(progress)}%</ProgressText>
        </>
      )}
      
      {showStats && stats && Object.keys(stats).length > 0 && (
        <StatsContainer>
          {stats.loadedCount !== undefined && stats.totalCount !== undefined && (
            <StatRow>
              <span>Загружено:</span>
              <span>{stats.loadedCount} из {stats.totalCount}</span>
            </StatRow>
          )}
          {stats.cacheSize !== undefined && (
            <StatRow>
              <span>В кэше:</span>
              <span>{stats.cacheSize} записей</span>
            </StatRow>
          )}
          {stats.cacheUsage !== undefined && (
            <StatRow>
              <span>Использование кэша:</span>
              <span>{stats.cacheUsage}</span>
            </StatRow>
          )}
        </StatsContainer>
      )}
    </LoadingContainer>
  );
};

/**
 * Компактный индикатор загрузки для встраивания
 */
export const CompactLoadingIndicator = ({ text = "Загрузка..." }) => (
  <div style={{ 
    display: 'flex', 
    alignItems: 'center', 
    justifyContent: 'center', 
    padding: '20px',
    gap: '12px'
  }}>
    <div style={{
      width: '20px',
      height: '20px',
      border: '2px solid #f3f3f3',
      borderTop: '2px solid #0066cc',
      borderRadius: '50%',
      animation: `${spin} 1s linear infinite`
    }} />
    <span style={{ color: '#666', fontSize: '14px' }}>{text}</span>
  </div>
);

/**
 * Скелетон для карточек товаров
 */
export const ProductCardSkeleton = () => {
  const SkeletonCard = styled.div`
    background: white;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    animation: ${pulse} 1.5s ease-in-out infinite;
  `;

  const SkeletonImage = styled.div`
    width: 100%;
    height: 200px;
    background: #f0f0f0;
    border-radius: 8px;
    margin-bottom: 12px;
  `;

  const SkeletonText = styled.div`
    height: 16px;
    background: #f0f0f0;
    border-radius: 4px;
    margin-bottom: 8px;
    width: ${props => props.width || '100%'};
  `;

  return (
    <SkeletonCard>
      <SkeletonImage />
      <SkeletonText width="80%" />
      <SkeletonText width="60%" />
      <SkeletonText width="40%" />
    </SkeletonCard>
  );
};

/**
 * Сетка скелетонов для списка товаров
 */
export const ProductGridSkeleton = ({ count = 6 }) => {
  const SkeletonGrid = styled.div`
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(272px, 1fr));
    gap: 24px;
    padding: 20px 0;
  `;

  return (
    <SkeletonGrid>
      {Array.from({ length: count }).map((_, index) => (
        <ProductCardSkeleton key={index} />
      ))}
    </SkeletonGrid>
  );
};

LoadingIndicator.displayName = "LoadingIndicator";
CompactLoadingIndicator.displayName = "CompactLoadingIndicator";
ProductCardSkeleton.displayName = "ProductCardSkeleton";
ProductGridSkeleton.displayName = "ProductGridSkeleton";

export default LoadingIndicator;
