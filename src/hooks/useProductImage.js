import { useState, useEffect } from "react";
import ApiService from "../services/api.service";
import { convertImagePath } from "../utils/imageUtils";

/**
 * Хук для загрузки изображения товара
 * @param {number|string} materialId - ID товара
 * @param {string} fallbackUrl - Запасное изображение
 * @returns {Object} - Объект с URL изображения и состоянием загрузки
 */
export const useProductImage = (materialId, fallbackUrl = "/images/CardImage.png") => {
  const [imageUrl, setImageUrl] = useState(fallbackUrl);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    if (!materialId) {
      setImageUrl(fallbackUrl);
      setIsLoading(false);
      setHasError(false);
      return;
    }

    const loadImage = async () => {
      try {
        setIsLoading(true);
        setHasError(false);

        // Получаем главную фотографию товара
        const photo = await ApiService.getProductMainPhoto(materialId);
        
        if (photo && photo.FileSrc) {
          const convertedUrl = convertImagePath(photo.FileSrc);
          
          // Проверяем, что изображение действительно загружается
          const img = new Image();
          img.onload = () => {
            setImageUrl(convertedUrl);
            setIsLoading(false);
          };
          img.onerror = () => {
            setImageUrl(fallbackUrl);
            setIsLoading(false);
            setHasError(true);
          };
          img.src = convertedUrl;
        } else {
          // Если фотография не найдена, используем запасное изображение
          setImageUrl(fallbackUrl);
          setIsLoading(false);
        }
      } catch (error) {
        console.error(`Ошибка при загрузке изображения для товара ${materialId}:`, error);
        setImageUrl(fallbackUrl);
        setIsLoading(false);
        setHasError(true);
      }
    };

    loadImage();
  }, [materialId, fallbackUrl]);

  return {
    imageUrl,
    isLoading,
    hasError,
  };
};

/**
 * Хук для загрузки всех изображений товара
 * @param {number|string} materialId - ID товара
 * @param {string} fallbackUrl - Запасное изображение
 * @returns {Object} - Объект с массивом изображений и состоянием загрузки
 */
export const useProductImages = (materialId, fallbackUrl = "/images/CardImage.png") => {
  const [images, setImages] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    if (!materialId) {
      setImages([{
        thumbnail: fallbackUrl,
        full: fallbackUrl,
        alt: "Изображение товара",
      }]);
      setIsLoading(false);
      setHasError(false);
      return;
    }

    const loadImages = async () => {
      try {
        setIsLoading(true);
        setHasError(false);

        // Получаем все фотографии товара
        const photos = await ApiService.getProductPhotos(materialId);
        
        if (photos && photos.length > 0) {
          const imagePromises = photos.map((photo, index) => {
            return new Promise((resolve) => {
              const convertedUrl = convertImagePath(photo.FileSrc);
              
              // Проверяем, что изображение действительно загружается
              const img = new Image();
              img.onload = () => {
                resolve({
                  thumbnail: convertedUrl,
                  full: convertedUrl,
                  alt: `Изображение товара ${index + 1}`,
                  fileName: photo.FileName,
                });
              };
              img.onerror = () => {
                resolve({
                  thumbnail: fallbackUrl,
                  full: fallbackUrl,
                  alt: `Изображение товара ${index + 1}`,
                  fileName: photo.FileName,
                  hasError: true,
                });
              };
              img.src = convertedUrl;
            });
          });

          const loadedImages = await Promise.all(imagePromises);
          
          // Фильтруем изображения без ошибок или добавляем хотя бы одно запасное
          const validImages = loadedImages.filter(img => !img.hasError);
          
          if (validImages.length > 0) {
            setImages(validImages);
          } else {
            // Если все изображения с ошибками, используем запасное
            setImages([{
              thumbnail: fallbackUrl,
              full: fallbackUrl,
              alt: "Изображение товара",
            }]);
          }
        } else {
          // Если фотографии не найдены, используем запасное изображение
          setImages([{
            thumbnail: fallbackUrl,
            full: fallbackUrl,
            alt: "Изображение товара",
          }]);
        }
        
        setIsLoading(false);
      } catch (error) {
        console.error(`Ошибка при загрузке изображений для товара ${materialId}:`, error);
        setImages([{
          thumbnail: fallbackUrl,
          full: fallbackUrl,
          alt: "Изображение товара",
        }]);
        setIsLoading(false);
        setHasError(true);
      }
    };

    loadImages();
  }, [materialId, fallbackUrl]);

  return {
    images,
    isLoading,
    hasError,
  };
};
