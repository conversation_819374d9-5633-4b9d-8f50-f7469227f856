import { useState, useEffect } from "react";
import ApiService from "../services/api.service";
import { convertImagePath } from "../utils/imageUtils";

/**
 * Хук для загрузки изображения товара
 * @param {number|string} materialId - ID товара
 * @returns {Object} - Объект с URL изображения и состоянием загрузки
 */
export const useProductImage = (materialId) => {
  const [imageUrl, setImageUrl] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    if (!materialId) {
      setImageUrl(null);
      setIsLoading(false);
      setHasError(false);
      return;
    }

    const loadImage = async () => {
      try {
        setIsLoading(true);
        setHasError(false);

        // Получаем главную фотографию товара
        const photo = await ApiService.getProductMainPhoto(materialId);

        if (photo && photo.FileSrc) {
          const convertedUrl = convertImagePath(photo.FileSrc);

          // Проверяем, что изображение действительно загружается
          const img = new Image();
          img.onload = () => {
            setImageUrl(convertedUrl);
            setIsLoading(false);
          };
          img.onerror = () => {
            setImageUrl(null); // Не показываем изображение при ошибке
            setIsLoading(false);
            setHasError(true);
          };
          img.src = convertedUrl;
        } else {
          // Если фотография не найдена, не показываем изображение
          setImageUrl(null);
          setIsLoading(false);
        }
      } catch (error) {
        console.error(
          `Ошибка при загрузке изображения для товара ${materialId}:`,
          error
        );
        setImageUrl(null); // Не показываем изображение при ошибке
        setIsLoading(false);
        setHasError(true);
      }
    };

    loadImage();
  }, [materialId]);

  return {
    imageUrl,
    isLoading,
    hasError,
  };
};

/**
 * Хук для загрузки всех изображений товара
 * @param {number|string} materialId - ID товара
 * @returns {Object} - Объект с массивом изображений и состоянием загрузки
 */
export const useProductImages = (materialId) => {
  const [images, setImages] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    if (!materialId) {
      setImages([]);
      setIsLoading(false);
      setHasError(false);
      return;
    }

    const loadImages = async () => {
      try {
        setIsLoading(true);
        setHasError(false);

        // Получаем все фотографии товара
        const photos = await ApiService.getProductPhotos(materialId);

        if (photos && photos.length > 0) {
          const imagePromises = photos.map((photo, index) => {
            return new Promise((resolve) => {
              const convertedUrl = convertImagePath(photo.FileSrc);

              // Проверяем, что изображение действительно загружается
              const img = new Image();
              img.onload = () => {
                resolve({
                  thumbnail: convertedUrl,
                  full: convertedUrl,
                  alt: `Изображение товара ${index + 1}`,
                  fileName: photo.FileName,
                });
              };
              img.onerror = () => {
                resolve(null); // Возвращаем null для неработающих изображений
              };
              img.src = convertedUrl;
            });
          });

          const loadedImages = await Promise.all(imagePromises);

          // Фильтруем только работающие изображения
          const validImages = loadedImages.filter((img) => img !== null);

          setImages(validImages);
        } else {
          // Если фотографии не найдены, оставляем пустой массив
          setImages([]);
        }

        setIsLoading(false);
      } catch (error) {
        console.error(
          `Ошибка при загрузке изображений для товара ${materialId}:`,
          error
        );
        setImages([]);
        setIsLoading(false);
        setHasError(true);
      }
    };

    loadImages();
  }, [materialId]);

  return {
    images,
    isLoading,
    hasError,
  };
};
