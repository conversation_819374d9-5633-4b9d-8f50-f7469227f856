import React from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import PerformanceApiService from "../services/api.service.performance";

/**
 * Хук для получения начальных продуктов (ограниченное количество)
 */
export const useInitialProducts = (options = {}) => {
  return useQuery({
    queryKey: ["initialProducts"],
    queryFn: () => PerformanceApiService.getInitialProducts(),
    staleTime: 5 * 60 * 1000, // 5 минут
    cacheTime: 10 * 60 * 1000, // 10 минут
    ...options,
  });
};

/**
 * Хук для получения всех продуктов (с умным кэшированием)
 */
export const useAllProducts = (options = {}) => {
  return useQuery({
    queryKey: ["allProducts"],
    queryFn: () => PerformanceApiService.getAllProducts(),
    staleTime: 5 * 60 * 1000, // 5 минут
    cacheTime: 15 * 60 * 1000, // 15 минут
    ...options,
  });
};

/**
 * Хук для получения продуктов с пагинацией
 */
export const useProductsWithPagination = (
  page = 1,
  pageSize = 20,
  options = {}
) => {
  return useQuery({
    queryKey: ["productsWithPagination", page, pageSize],
    queryFn: () =>
      PerformanceApiService.getProductsWithPagination(page, pageSize),
    keepPreviousData: true,
    staleTime: 2 * 60 * 1000, // 2 минуты
    ...options,
  });
};

/**
 * Хук для поиска продуктов с оптимизацией
 */
export const useSearchProducts = (query, options = {}) => {
  return useQuery({
    queryKey: ["searchProducts", query],
    queryFn: () => PerformanceApiService.searchProducts(query),
    enabled: query !== undefined, // Выполнять даже для пустого запроса
    staleTime: 3 * 60 * 1000, // 3 минуты
    ...options,
  });
};

/**
 * Хук для фильтрации продуктов по категории
 */
export const useFilteredProducts = (category, options = {}) => {
  const queryKey = [
    "filteredProducts",
    category?.department,
    category?.section,
    category?.subsection,
    category?.group,
  ];

  return useQuery({
    queryKey: queryKey,
    queryFn: () => PerformanceApiService.filterProductsByCategory(category),
    enabled: true, // Всегда включен, так как может возвращать начальные продукты
    staleTime: 3 * 60 * 1000, // 3 минуты
    ...options,
  });
};

/**
 * Хук для получения детальной информации о продукте
 */
export const useProductDetails = (productId, options = {}) => {
  return useQuery({
    queryKey: ["productDetails", productId],
    queryFn: () => PerformanceApiService.getProductById(productId),
    enabled: !!productId,
    staleTime: 10 * 60 * 1000, // 10 минут
    ...options,
  });
};

/**
 * Хук для получения каталога
 */
export const useCatalog = (options = {}) => {
  return useQuery({
    queryKey: ["catalog"],
    queryFn: () => PerformanceApiService.getCatalog(),
    staleTime: 15 * 60 * 1000, // 15 минут
    cacheTime: 30 * 60 * 1000, // 30 минут
    ...options,
  });
};

/**
 * Хук для получения фотографий
 */
export const usePhotos = (options = {}) => {
  return useQuery({
    queryKey: ["photos"],
    queryFn: () => PerformanceApiService.getPhotos(),
    staleTime: 10 * 60 * 1000, // 10 минут
    cacheTime: 20 * 60 * 1000, // 20 минут
    retry: 1, // Только одна попытка для фотографий
    ...options,
  });
};

/**
 * Хук для получения статистики производительности
 */
export const usePerformanceStats = () => {
  const queryClient = useQueryClient();

  return {
    getStats: () => PerformanceApiService.getPerformanceStats(),
    clearCache: () => {
      PerformanceApiService.clearCache();
      queryClient.clear(); // Очищаем также кэш React Query
    },
    invalidateAll: () => {
      queryClient.invalidateQueries(); // Инвалидируем все запросы
    },
  };
};

/**
 * Хук для предзагрузки данных
 */
export const usePrefetchData = () => {
  const queryClient = useQueryClient();

  const prefetchAllProducts = () => {
    queryClient.prefetchQuery({
      queryKey: ["allProducts"],
      queryFn: () => PerformanceApiService.getAllProducts(),
      staleTime: 5 * 60 * 1000,
    });
  };

  const prefetchCatalog = () => {
    queryClient.prefetchQuery({
      queryKey: ["catalog"],
      queryFn: () => PerformanceApiService.getCatalog(),
      staleTime: 15 * 60 * 1000,
    });
  };

  const prefetchPhotos = () => {
    queryClient.prefetchQuery({
      queryKey: ["photos"],
      queryFn: () => PerformanceApiService.getPhotos(),
      staleTime: 10 * 60 * 1000,
    });
  };

  return {
    prefetchAllProducts,
    prefetchCatalog,
    prefetchPhotos,
    prefetchAll: () => {
      prefetchAllProducts();
      prefetchCatalog();
      prefetchPhotos();
    },
  };
};

/**
 * Хук для мониторинга состояния загрузки
 */
export const useLoadingState = () => {
  const initialProducts = useInitialProducts();
  const allProducts = useAllProducts({ enabled: false }); // Не загружаем автоматически

  const isInitialLoading = initialProducts.isLoading;
  const isBackgroundLoading = allProducts.isFetching;
  const hasError = initialProducts.error || allProducts.error;

  const loadingProgress = (() => {
    if (isInitialLoading) return 0;
    if (initialProducts.data && !allProducts.data) return 50;
    if (allProducts.data) return 100;
    return 0;
  })();

  return {
    isInitialLoading,
    isBackgroundLoading,
    hasError,
    loadingProgress,
    initialData: initialProducts.data,
    allData: allProducts.data,
    refetchInitial: initialProducts.refetch,
    refetchAll: allProducts.refetch,
  };
};

/**
 * Хук для умной загрузки (сначала начальные, потом все)
 */
export const useSmartProductLoading = () => {
  const queryClient = useQueryClient();
  const { prefetchAllProducts } = usePrefetchData();

  // Загружаем начальные продукты
  const initialQuery = useInitialProducts();

  // Когда начальные продукты загружены, начинаем предзагрузку всех
  React.useEffect(() => {
    if (initialQuery.data && !initialQuery.isLoading) {
      // Небольшая задержка, чтобы не блокировать UI
      const timer = setTimeout(() => {
        prefetchAllProducts();
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [initialQuery.data, initialQuery.isLoading, prefetchAllProducts]);

  // Проверяем, загружены ли все продукты
  const allProductsData = queryClient.getQueryData(["allProducts"]);

  return {
    data: initialQuery.data,
    isLoading: initialQuery.isLoading,
    error: initialQuery.error,
    hasAllProducts: !!allProductsData,
    refetch: initialQuery.refetch,
  };
};

export default {
  useInitialProducts,
  useAllProducts,
  useProductsWithPagination,
  useSearchProducts,
  useFilteredProducts,
  useProductDetails,
  useCatalog,
  usePhotos,
  usePerformanceStats,
  usePrefetchData,
  useLoadingState,
  useSmartProductLoading,
};
