import API_CONFIG from "../config/api";

// Улучшенный кэш с TTL и ограничением размера
class PerformanceCache {
  constructor() {
    this.cache = new Map();
    this.timestamps = new Map();
    this.maxSize = API_CONFIG.PERFORMANCE.MAX_CACHE_SIZE;
    this.ttl = API_CONFIG.PERFORMANCE.CACHE_TTL;
  }

  set(key, value) {
    // Очищаем старые записи если кэш переполнен
    if (this.cache.size >= this.maxSize) {
      this.cleanup();
    }

    this.cache.set(key, value);
    this.timestamps.set(key, Date.now());
  }

  get(key) {
    const timestamp = this.timestamps.get(key);

    // Проверяем, не истек ли TTL
    if (timestamp && Date.now() - timestamp > this.ttl) {
      this.cache.delete(key);
      this.timestamps.delete(key);
      return null;
    }

    return this.cache.get(key) || null;
  }

  has(key) {
    return this.get(key) !== null;
  }

  cleanup() {
    const now = Date.now();
    const keysToDelete = [];

    // Находим устаревшие записи
    for (const [key, timestamp] of this.timestamps.entries()) {
      if (now - timestamp > this.ttl) {
        keysToDelete.push(key);
      }
    }

    // Удаляем устаревшие записи
    keysToDelete.forEach((key) => {
      this.cache.delete(key);
      this.timestamps.delete(key);
    });

    // Если все еще переполнен, удаляем самые старые
    if (this.cache.size >= this.maxSize) {
      const sortedEntries = Array.from(this.timestamps.entries()).sort(
        (a, b) => a[1] - b[1]
      );

      const toDelete = sortedEntries.slice(0, Math.floor(this.maxSize * 0.3));
      toDelete.forEach(([key]) => {
        this.cache.delete(key);
        this.timestamps.delete(key);
      });
    }
  }

  clear() {
    this.cache.clear();
    this.timestamps.clear();
  }

  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      usage: `${Math.round((this.cache.size / this.maxSize) * 100)}%`,
    };
  }
}

// Создаем экземпляр кэша
const performanceCache = new PerformanceCache();

// Состояние загрузки
const loadingState = {
  isInitialLoading: false,
  loadedCount: 0,
  totalCount: 0,
  error: null,
};

/**
 * Функция для выполнения HTTP запросов с улучшенной обработкой ошибок
 */
const fetchApi = async (url, options = {}) => {
  try {
    console.log(`🚀 API запрос: ${url}`);
    const startTime = Date.now();

    const response = await fetch(url, {
      ...API_CONFIG.REQUEST_OPTIONS,
      ...options,
    });

    const endTime = Date.now();
    console.log(`⏱️ Запрос выполнен за ${endTime - startTime}ms`);

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const data = await response.json();
    console.log(
      `📦 Получено записей: ${Array.isArray(data) ? data.length : "объект"}`
    );

    return data;
  } catch (error) {
    console.error("❌ API request error:", error);
    throw error;
  }
};

/**
 * Оптимизированный сервис для работы с API
 */
const PerformanceApiService = {
  /**
   * Получить ограниченное количество продуктов для первоначальной загрузки
   */
  getInitialProducts: async () => {
    const cacheKey = "initial_products";

    // Проверяем кэш
    if (performanceCache.has(cacheKey)) {
      console.log("📋 Возвращаем товары из кэша");
      return performanceCache.get(cacheKey);
    }

    try {
      loadingState.isInitialLoading = true;
      loadingState.error = null;

      // Получаем все товары с API
      const allProducts = await fetchApi(
        `${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.PRODUCTS}`
      );

      // Ограничиваем количество для первоначальной загрузки
      const limitedProducts = allProducts.slice(
        0,
        API_CONFIG.PERFORMANCE.INITIAL_LOAD_LIMIT
      );

      // Сохраняем в кэш
      performanceCache.set(cacheKey, limitedProducts);
      performanceCache.set("all_products", allProducts); // Сохраняем все для последующего использования

      loadingState.loadedCount = limitedProducts.length;
      loadingState.totalCount = allProducts.length;

      console.log(
        `✅ Загружено ${limitedProducts.length} из ${allProducts.length} товаров`
      );

      return limitedProducts;
    } catch (error) {
      loadingState.error = error.message;
      throw error;
    } finally {
      loadingState.isInitialLoading = false;
    }
  },

  /**
   * Получить все продукты (с умным кэшированием)
   */
  getAllProducts: async () => {
    const cacheKey = "all_products";

    // Проверяем кэш
    if (performanceCache.has(cacheKey)) {
      console.log("📋 Возвращаем все товары из кэша");
      return performanceCache.get(cacheKey);
    }

    try {
      const allProducts = await fetchApi(
        `${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.PRODUCTS}`
      );

      // Сохраняем в кэш
      performanceCache.set(cacheKey, allProducts);

      console.log(`✅ Загружено ${allProducts.length} товаров`);

      return allProducts;
    } catch (error) {
      console.error("❌ Ошибка при получении всех товаров:", error);
      throw error;
    }
  },

  /**
   * Получить продукты с пагинацией (клиентская)
   */
  getProductsWithPagination: async (
    page = 1,
    pageSize = API_CONFIG.PERFORMANCE.DEFAULT_PAGE_SIZE
  ) => {
    const allProducts = await PerformanceApiService.getAllProducts();

    const startIndex = (page - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize, allProducts.length);
    const paginatedProducts = allProducts.slice(startIndex, endIndex);

    return {
      data: paginatedProducts,
      pagination: {
        page,
        pageSize,
        totalItems: allProducts.length,
        totalPages: Math.ceil(allProducts.length / pageSize),
        hasNextPage: page < Math.ceil(allProducts.length / pageSize),
        hasPrevPage: page > 1,
      },
    };
  },

  /**
   * Поиск продуктов с оптимизацией
   */
  searchProducts: async (query) => {
    if (!query) {
      return PerformanceApiService.getInitialProducts();
    }

    const cacheKey = `search_${query.toLowerCase()}`;

    // Проверяем кэш
    if (performanceCache.has(cacheKey)) {
      console.log(`📋 Возвращаем результаты поиска из кэша: "${query}"`);
      return performanceCache.get(cacheKey);
    }

    const allProducts = await PerformanceApiService.getAllProducts();
    const lowerQuery = query.toLowerCase();

    const searchResults = allProducts.filter(
      (product) =>
        product.MaterialName &&
        product.MaterialName.toLowerCase().includes(lowerQuery)
    );

    // Сохраняем результаты поиска в кэш
    performanceCache.set(cacheKey, searchResults);

    console.log(
      `🔍 Найдено ${searchResults.length} товаров по запросу: "${query}"`
    );

    return searchResults;
  },

  /**
   * Фильтрация по категории с оптимизацией (используем правильную логику из старого API)
   */
  filterProductsByCategory: async (category) => {
    if (
      !category ||
      (!category.department &&
        !category.section &&
        !category.subsection &&
        !category.group)
    ) {
      return PerformanceApiService.getInitialProducts();
    }

    const cacheKey = `filter_${category.department || ""}_${
      category.section || ""
    }_${category.subsection || ""}_${category.group || ""}`;

    // Проверяем кэш
    if (performanceCache.has(cacheKey)) {
      console.log("📋 Возвращаем отфильтрованные товары из кэша");
      return performanceCache.get(cacheKey);
    }

    try {
      // Получаем все продукты и каталог
      const allProducts = await PerformanceApiService.getAllProducts();
      const catalogData = await PerformanceApiService.getCatalog();

      // Используем правильную логику фильтрации из старого API
      const filteredProducts = PerformanceApiService._filterProductsByCategory(
        allProducts,
        catalogData,
        category
      );

      // Сохраняем результаты фильтрации в кэш
      performanceCache.set(cacheKey, filteredProducts);

      console.log(`🔽 Отфильтровано ${filteredProducts.length} товаров`);

      return filteredProducts;
    } catch (error) {
      console.error("❌ Ошибка при фильтрации товаров:", error);
      throw error;
    }
  },

  /**
   * Вспомогательный метод для фильтрации продуктов по категории (из старого API)
   * @private
   */
  _filterProductsByCategory: (products, catalogData, category) => {
    let filtered = products;
    console.log("🔍 Фильтрация продуктов по категории:", category);

    // Находим связанные категории
    const relatedCategories = PerformanceApiService._findRelatedCategories(
      catalogData,
      category
    );

    // Фильтруем продукты по связанным категориям
    if (relatedCategories.length > 0) {
      filtered = filtered.filter((product) =>
        relatedCategories.includes(product.MaterialTreeId)
      );
    }

    console.log("✅ Отфильтрованные продукты:", filtered.length);
    return filtered;
  },

  /**
   * Вспомогательный метод для поиска связанных категорий (из старого API)
   * @private
   */
  _findRelatedCategories: (catalogData, category) => {
    const relatedCategories = [];

    if (category.group) {
      // Если выбрана группа, добавляем только её
      const groupId = parseInt(category.group);
      relatedCategories.push(groupId);

      // Добавляем все продукты, которые относятся к этой группе
      const products = catalogData.filter((item) => item.ParId === groupId);
      products.forEach((product) => {
        relatedCategories.push(product.MaterialTreeId);
      });
    } else if (category.subsection) {
      // Если выбран подраздел, добавляем его
      const subsectionId = parseInt(category.subsection);
      relatedCategories.push(subsectionId);

      // Находим все группы подраздела
      const groups = catalogData.filter((item) => item.ParId === subsectionId);
      groups.forEach((group) => {
        relatedCategories.push(group.MaterialTreeId);

        // Добавляем все продукты, которые относятся к этим группам
        const products = catalogData.filter(
          (item) => item.ParId === group.MaterialTreeId
        );
        products.forEach((product) => {
          relatedCategories.push(product.MaterialTreeId);
        });
      });
    } else if (category.section) {
      // Если выбран раздел, добавляем его
      const sectionId = parseInt(category.section);
      relatedCategories.push(sectionId);

      // Находим все подразделы раздела
      const subsections = catalogData.filter(
        (item) => item.ParId === sectionId
      );
      subsections.forEach((subsection) => {
        relatedCategories.push(subsection.MaterialTreeId);

        // Находим все группы подраздела
        const groups = catalogData.filter(
          (item) => item.ParId === subsection.MaterialTreeId
        );
        groups.forEach((group) => {
          relatedCategories.push(group.MaterialTreeId);

          // Добавляем все продукты, которые относятся к этим группам
          const products = catalogData.filter(
            (item) => item.ParId === group.MaterialTreeId
          );
          products.forEach((product) => {
            relatedCategories.push(product.MaterialTreeId);
          });
        });
      });
    } else if (category.department) {
      // Если выбран отдел, добавляем его
      const departmentId = parseInt(category.department);
      relatedCategories.push(departmentId);

      // Находим все разделы отдела
      const sections = catalogData.filter(
        (item) => item.ParId === departmentId
      );
      sections.forEach((section) => {
        relatedCategories.push(section.MaterialTreeId);

        // Находим все подразделы раздела
        const subsections = catalogData.filter(
          (item) => item.ParId === section.MaterialTreeId
        );
        subsections.forEach((subsection) => {
          relatedCategories.push(subsection.MaterialTreeId);

          // Находим все группы подраздела
          const groups = catalogData.filter(
            (item) => item.ParId === subsection.MaterialTreeId
          );
          groups.forEach((group) => {
            relatedCategories.push(group.MaterialTreeId);

            // Добавляем все продукты, которые относятся к этим группам
            const products = catalogData.filter(
              (item) => item.ParId === group.MaterialTreeId
            );
            products.forEach((product) => {
              relatedCategories.push(product.MaterialTreeId);
            });
          });
        });
      });
    }

    console.log("🔗 Связанные категории:", relatedCategories);
    return relatedCategories;
  },

  /**
   * Получить детальную информацию о продукте
   */
  getProductById: async (id) => {
    const cacheKey = `product_${id}`;

    // Проверяем кэш
    if (performanceCache.has(cacheKey)) {
      console.log(`📋 Возвращаем товар ${id} из кэша`);
      return performanceCache.get(cacheKey);
    }

    // Сначала пытаемся найти в загруженных товарах
    const allProducts = performanceCache.get("all_products");
    if (allProducts) {
      const product = allProducts.find(
        (p) => p.MaterialId.toString() === id.toString()
      );
      if (product) {
        performanceCache.set(cacheKey, product);
        return product;
      }
    }

    // Если не найден, запрашиваем с API
    const url = `${
      API_CONFIG.BASE_URL
    }${API_CONFIG.ENDPOINTS.PRODUCT_DETAILS.replace(":id", id)}`;
    const product = await fetchApi(url);

    performanceCache.set(cacheKey, product);

    return product;
  },

  /**
   * Получить каталог
   */
  getCatalog: async () => {
    const cacheKey = "catalog";

    // Проверяем кэш
    if (performanceCache.has(cacheKey)) {
      console.log("📋 Возвращаем каталог из кэша");
      return performanceCache.get(cacheKey);
    }

    const catalog = await fetchApi(
      `${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.CATALOG}`
    );

    performanceCache.set(cacheKey, catalog);

    return catalog;
  },

  /**
   * Получить фотографии
   */
  getPhotos: async () => {
    const cacheKey = "photos";

    // Проверяем кэш
    if (performanceCache.has(cacheKey)) {
      console.log("📋 Возвращаем фотографии из кэша");
      return performanceCache.get(cacheKey);
    }

    try {
      const photos = await fetchApi(
        `${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.PHOTOS}`
      );

      performanceCache.set(cacheKey, photos);

      return photos;
    } catch (error) {
      console.error("❌ Ошибка при получении фотографий:", error);
      return [];
    }
  },

  /**
   * Получить статистику производительности
   */
  getPerformanceStats: () => {
    return {
      cache: performanceCache.getStats(),
      loading: { ...loadingState },
    };
  },

  /**
   * Очистить кэш
   */
  clearCache: () => {
    performanceCache.clear();
    console.log("🗑️ Кэш очищен");
  },
};

export default PerformanceApiService;
